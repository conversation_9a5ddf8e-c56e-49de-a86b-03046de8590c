import {
  Controller,
  Post,
  Get,
  Body,
  Query,
  Headers,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';

import { Res } from 'src/common/dto/res.dto';
import { ErrorCode } from 'src/error/error-code';

import { ChatService } from './chat.service';

@ApiTags('聊天会话管理')
@Controller('/v1/chat')
export class ChatController {
  constructor(private readonly chatService: ChatService) {}

  @Post('/create_session')
  @ApiOperation({
    summary: '创建会话',
  })
  async createSession(@Body() payload: any, @Headers() headers: any) {
    try {
      const result = await this.chatService.createSession(payload, headers);
      return new Res().success(result);
    } catch (error) {
      console.error('创建会话失败:', error);
      if (error.response) {
        // 转发上游服务的错误响应
        throw new HttpException(error.response.data, error.response.status);
      }
      return new Res().error(new ErrorCode().DEMO_UNKNOWN, '创建会话失败');
    }
  }

  @Get('/get_history_sessions')
  @ApiOperation({
    summary: '获取历史会话列表',
  })
  async getHistorySessions(@Query() params: any, @Headers() headers: any) {
    try {
      const result = await this.chatService.getHistorySessions(params, headers);
      return new Res().success(result);
    } catch (error) {
      console.error('获取历史会话列表失败:', error);
      if (error.response) {
        // 转发上游服务的错误响应
        throw new HttpException(error.response.data, error.response.status);
      }
      return new Res().error(new ErrorCode().DEMO_UNKNOWN, '获取历史会话列表失败');
    }
  }

  @Get('/get_history_messages')
  @ApiOperation({
    summary: '获取历史消息详情',
  })
  async getHistoryMessages(@Query() params: any, @Headers() headers: any) {
    try {
      const result = await this.chatService.getHistoryMessages(params, headers);
      return new Res().success(result);
    } catch (error) {
      console.error('获取历史消息详情失败:', error);
      if (error.response) {
        // 转发上游服务的错误响应
        throw new HttpException(error.response.data, error.response.status);
      }
      return new Res().error(new ErrorCode().DEMO_UNKNOWN, '获取历史消息详情失败');
    }
  }

  @Post('/feedback')
  @ApiOperation({
    summary: '点赞/点踩接口',
  })
  async feedback(@Body() payload: any, @Headers() headers: any) {
    try {
      const result = await this.chatService.feedback(payload, headers);
      return new Res().success(result);
    } catch (error) {
      console.error('反馈操作失败:', error);
      if (error.response) {
        // 转发上游服务的错误响应
        throw new HttpException(error.response.data, error.response.status);
      }
      return new Res().error(new ErrorCode().DEMO_UNKNOWN, '反馈操作失败');
    }
  }
}
