import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class ChatService {
  private readonly baseUrl: string;

  constructor(private readonly httpService: HttpService) {
    // 根据环境变量判断使用的URL
    const region = process.env.REGION || 'local';
    this.baseUrl = 'http://10.102.92.209:9607/ai/orchestration/session';
  }

  /**
   * 创建会话
   */
  async createSession(payload: any, headers: any) {
    const url = `${this.baseUrl}/create_session`;
    const response = await firstValueFrom(
      this.httpService.post(url, payload, { headers })
    );
    return response.data;
  }

  /**
   * 获取历史会话列表
   */
  async getHistorySessions(params: any, headers: any) {
    const url = `${this.baseUrl}/get_history_sessions`;
    const response = await firstValueFrom(
      this.httpService.get(url, { params, headers })
    );
    return response.data;
  }

  /**
   * 获取历史消息详情
   */
  async getHistoryMessages(params: any, headers: any) {
    const url = `${this.baseUrl}/get_history_messages`;
    const response = await firstValueFrom(
      this.httpService.get(url, { params, headers })
    );
    return response.data;
  }

  /**
   * 点赞/点踩接口
   */
  async feedback(payload: any, headers: any) {
    const url = `${this.baseUrl}/feedback`;
    const response = await firstValueFrom(
      this.httpService.post(url, payload, { headers })
    );
    return response.data;
  }
}
